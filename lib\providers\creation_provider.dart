import 'package:flutter/material.dart';

class CreationProvider extends ChangeNotifier {
  int _currentMiddleScreen = 1;
  // 0 - roles 1- objects 2-GO
  bool _isGoMyLibraryClicked = false;

  int get currentMiddleScreen => _currentMiddleScreen;
  bool get isGoMyLibraryClicked => _isGoMyLibraryClicked;

  set currentMiddleScreen(value) {
    _currentMiddleScreen = value;
    notifyListeners();
  }

  set isGoMyLibraryClicked(value) {
    _isGoMyLibraryClicked = value;
    notifyListeners();
  }
}
