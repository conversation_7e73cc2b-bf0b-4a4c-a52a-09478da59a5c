name: nsl
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.32+29

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  fl_chart: 1.0.0
  pie_chart: ^5.3.2

  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  http: ^1.2.1
  dio: ^5.4.1
  provider: ^6.1.2
  flutter_markdown: ^0.6.22
  shared_preferences: ^2.2.2
  flutter_dotenv: ^5.1.0
  animated_text_kit: ^4.2.2
  intl: ^0.19.0
  change_app_package_name: ^1.4.0
  package_info_plus: ^8.3.0
  image_picker: ^1.0.7
  file_picker: ^10.1.2
  path_provider: ^2.1.2
  http_parser: ^4.0.2
  permission_handler: ^11.3.0
  speech_to_text: ^7.0.0
  flutter_tts: ^3.8.5
  flutter_svg: ^2.0.10
  animated_tree_view: ^2.3.0
  flutter_quill: ^11.4.0
  flutter_launcher_icons: ^0.14.3
  google_maps_flutter: ^2.6.0
  geolocator: ^11.0.0
  geocoding: ^2.2.0
  record: ^6.0.0
  url_launcher: ^6.2.5
  audioplayers: ^6.0.0
  universal_html: ^2.2.4
  waveform_recorder: ^1.7.0
  toggle_switch: ^2.3.0
  carousel_slider: ^5.0.0 # or the latest version
  smooth_page_indicator: ^1.1.0
  flutter_highlight: ^0.7.0
  highlight: ^0.7.0
  overlay_loader_with_app_icon: ^0.0.4
  visibility_detector: ^0.4.0+2
  graphview: ^1.1.1
  rich_readmore: ^1.1.1
  typewritertext: ^3.0.9
  auto_size_text: ^3.0.0
  expansion_tile_group: ^2.3.0
  reorderables: ^0.6.0
  markdown_widget: ^2.3.2+8
  eventflux: ^2.2.1
  flutter_keyboard_visibility: ^6.0.0
  multi_dropdown: ^3.0.1
  dropdown_button2: ^2.3.9
  excel: ^4.0.6


dev_dependencies:
  flutter_test:
    sdk: flutter
dependency_overrides:
  ui_controls_library:
    path: ./ui_controls_library/ui_controls_library/
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0



# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.

flutter_launcher_icons:
  android: "ic_launcher"
  ios: true
  image_path: "assets/images/login_logo.png"
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/login_logo.png" 

flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/env
    - assets/images/
    - assets/images/icons/
    - assets/images/sidebar_icons/
    - assets/images/library_side_drawer/
    - assets/images/entity/
    - assets/images/my_business/
    - assets/images/my_business/collections/
    - assets/images/my_business/solutions/
    - assets/images/my_business/home/
    - assets/images/chat/
    - assets/images/workflow/
    - assets/images/object-flow-mobile/
    - assets/data/
    - assets/data/manual_validation/
    - assets/l10n/
    - lib/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/json/
    - assets/images/my_library/

  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SFProText
      fonts:
        - asset: assets/fonts/SF-Pro-Text-Regular.otf
          weight: 400
        - asset: assets/fonts/SF-Pro-Text-Medium.otf
          weight: 500
        - asset: assets/fonts/SF-Pro-Text-Semibold.otf
          weight: 600
        - asset: assets/fonts/SF-Pro-Text-Bold.otf
          weight: 700
    - family: TiemposText
      fonts:
        - asset: assets/fonts/TestTiemposText-Regular.otf
          weight: 400
        - asset: assets/fonts/TestTiemposText-Medium.otf
          weight: 500
        - asset: assets/fonts/TestTiemposText-Semibold.otf
          weight: 600
        - asset: assets/fonts/TestTiemposText-Bold.otf
          weight: 700
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-VariableFont_opsz,wght.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
