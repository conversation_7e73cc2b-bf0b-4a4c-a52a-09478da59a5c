import 'package:flutter/material.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/providers/create_entity_provider.dart';
import 'package:nsl/providers/creation_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/theme/app_colors.dart';
import 'my_library_service.dart';
import 'get_all_my_library_model.dart' hide Icon;
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/providers/accordion_availability_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:provider/provider.dart';

class WebLeftPanelSolution extends StatefulWidget {
  const WebLeftPanelSolution({super.key});

  @override
  State<WebLeftPanelSolution> createState() => _WebLeftPanelSolutionState();
}

class _WebLeftPanelSolutionState extends State<WebLeftPanelSolution>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All'; // State for dropdown selection

  // API data for entities and attributes
  List<Map<String, dynamic>> _objectsData = [];
  Map<String, List<String>> _entityAttributes =
      {}; // Cache for entity attributes
  bool _isLoadingEntities = true;
  bool _isLoadingAttributes = false;

  // API data for roles
  List<String> _rolesData = [];
  bool _isLoadingRoles = true;

  // API data for GO (Global Objectives)
  List<Map<String, dynamic>> _goData = [];
  Map<String, List<String>> _goLocalObjectives =
      {}; // Cache for GO local objectives
  bool _isLoadingGO = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: 3,
        vsync: this,
        initialIndex: Provider.of<CreationProvider>(context, listen: false)
            .currentMiddleScreen); // Start with Objects tab selected
    _fetchAllLibraryData();
  }

  @override
  didUpdateWidget(oldWidget) {
    _tabController.index = Provider.of<CreationProvider>(context, listen: false)
        .currentMiddleScreen;
    super.didUpdateWidget(oldWidget);
  }

  /// Fetch all library data from API in a single call
  Future<void> _fetchAllLibraryData() async {
    setState(() {
      _isLoadingRoles = true;
      _isLoadingEntities = true;
      _isLoadingGO = true;
    });

    try {
     
     final libraryResponse = await MyLibraryService.getAllLibraryDataForCurrentUser();
      if (libraryResponse != null &&
          libraryResponse.data?.dataLibrary != null) {
        final library = libraryResponse.data!.dataLibrary!;

        // Cache the library data for reuse
        _cachedLibraryData = library;

        // Process Roles
        final rolesList = <String>[];
        if (library.roles?.postgres != null) {
          for (final role in library.roles!.postgres!) {
            if (role.name != null) {
              rolesList.add(role.name!);
            }
          }
        }

        // Process Entities
        final entitiesList = <Map<String, dynamic>>[];
        if (library.entities?.postgres != null) {
          for (final entity in library.entities!.postgres!) {
            entitiesList.add({
              'entityId': entity.entityId ?? '',
              'name': entity.name ?? 'Unknown Entity',
              'displayName':
                  entity.displayName ?? entity.name ?? 'Unknown Entity',
              'attributes': <String>[],
              'isExpanded': false,
              'attributesLoaded': false,
              'status': 'published', // From postgres = published
            });
          }
        }
        if (library.entities?.mongoDrafts != null) {
          for (final entity in library.entities!.mongoDrafts!) {
            entitiesList.add({
              'entityId': entity.entityId ?? '',
              'name': entity.name ?? 'Unknown Entity',
              'displayName':
                  entity.displayName ?? entity.name ?? 'Unknown Entity',
              'attributes': <String>[],
              'isExpanded': false,
              'attributesLoaded': false,
              'status': 'draft', // From mongoDrafts = draft
            });
          }
        }

        // Process Global Objectives
        final goList = <Map<String, dynamic>>[];
        if (library.globalObjectives?.postgres != null) {
          for (final go in library.globalObjectives!.postgres!) {
            if (go.name != null && go.goId != null) {
              goList.add({
                'goId': go.goId!,
                'name': go.name!,
                'description': go.description ?? '', // Add description field
                'localObjectives': <String>[],
                'isExpanded': false,
                'localObjectivesLoaded': false,
              });
            }
          }
        }

        setState(() {
          _rolesData = rolesList.isNotEmpty
              ? rolesList
              : [
                  'Administrator',
                  'Manager',
                  'Developer',
                  'Analyst',
                  'Customer Support',
                  'Sales Representative',
                  'Marketing Specialist',
                ];
          _objectsData = entitiesList;
          _goData = goList; // Only use API data, no fallback
          _isLoadingRoles = false;
          _isLoadingEntities = false;
          _isLoadingGO = false;
        });

        print('Successfully fetched all library data in single API call');
        print('Global Objectives found: ${goList.length}');
        for (final go in goList) {
          print('  - GO: ${go['name']} (ID: ${go['goId']})');
        }

        // Debug: Check if local objectives data exists
        if (library.localObjectives?.postgres != null) {
          print(
              'Local Objectives found: ${library.localObjectives!.postgres!.length}');
          for (final lo in library.localObjectives!.postgres!) {
            print('  - LO: ${lo.name} (GO ID: ${lo.goId})');
          }
        } else {
          print('No Local Objectives found in API response');
        }
      } else {
        // Fallback to static data if API fails
        setState(() {
          _rolesData = [
            'Administrator',
            'Manager',
            'Developer',
            'Analyst',
            'Customer Support',
            'Sales Representative',
            'Marketing Specialist',
          ];
          _objectsData = [];
          _goData = []; // No fallback data, only API data
          _isLoadingRoles = false;
          _isLoadingEntities = false;
          _isLoadingGO = false;
        });
        print('Failed to fetch library data, using fallback data');
      }
    } catch (e) {
      // Fallback to static data if API fails
      setState(() {
        _rolesData = [
          'Administrator',
          'Manager',
          'Developer',
          'Analyst',
          'Customer Support',
          'Sales Representative',
          'Marketing Specialist',
        ];
        _objectsData = [];
        _goData = []; // No fallback data, only API data
        _isLoadingRoles = false;
        _isLoadingEntities = false;
        _isLoadingGO = false;
      });
      print('Error fetching library data: $e');
    }
  }

  // Store the library data for reuse
  Library? _cachedLibraryData;

  /// Fetch local objectives for a specific GO from cached library data
  Future<void> _fetchGoLocalObjectives(String goId, int goIndex) async {
    if (_goLocalObjectives.containsKey(goId)) {
      // Use cached local objectives
      setState(() {
        _goData[goIndex]['localObjectives'] = _goLocalObjectives[goId]!;
        _goData[goIndex]['localObjectivesLoaded'] = true;
      });
      return;
    }

    try {
      List<String> localObjectiveNames = [];

      // Use cached library data if available
      if (_cachedLibraryData?.localObjectives != null) {
        final allLocalObjectives = <LocalObjectivesPostgre>[];

        // Add postgres local objectives
        if (_cachedLibraryData!.localObjectives!.postgres != null) {
          allLocalObjectives
              .addAll(_cachedLibraryData!.localObjectives!.postgres!);
        }

        // Filter ALL local objectives that match this GO ID
        final goLocalObjectives =
            allLocalObjectives.where((lo) => lo.goId == goId).toList();

        // Extract names from all matching local objectives
        localObjectiveNames = goLocalObjectives
            .map((lo) => lo.name ?? 'Unknown Local Objective')
            .where((name) => name.isNotEmpty) // Filter out empty names
            .toList();

        print(
            'Found ${goLocalObjectives.length} local objectives for GO $goId');
        for (final lo in goLocalObjectives) {
          print('  - LO: ${lo.name} (ID: ${lo.loId})');
        }
      }

      // Debug: Print detailed information about local objectives search
      print('Searching for Local Objectives with GO ID: $goId');
      print(
          'Total Local Objectives in cache: ${_cachedLibraryData?.localObjectives?.postgres?.length ?? 0}');

      if (_cachedLibraryData?.localObjectives?.postgres != null) {
        print('All Local Objectives in API:');
        for (final lo in _cachedLibraryData!.localObjectives!.postgres!) {
          print(
              '  - LO: "${lo.name}" | GO ID: "${lo.goId}" | LO ID: "${lo.loId}"');
        }
        print('Filtering for GO ID: "$goId"');
        final matchingLOs = _cachedLibraryData!.localObjectives!.postgres!
            .where((lo) => lo.goId == goId)
            .toList();
        print('Found ${matchingLOs.length} matching Local Objectives');
      }

      // Cache the local objectives
      _goLocalObjectives[goId] = localObjectiveNames;

      setState(() {
        _goData[goIndex]['localObjectives'] = localObjectiveNames;
        _goData[goIndex]['localObjectivesLoaded'] = true;
      });

      print(
          'Successfully loaded ${localObjectiveNames.length} local objectives for GO $goId');
    } catch (e) {
      setState(() {
        _goData[goIndex]['localObjectives'] = <String>[];
        _goData[goIndex]['localObjectivesLoaded'] = true;
      });
      print('Error fetching local objectives for GO $goId: $e');
    }
  }

  /// Build add icon with conditional enabling/disabling based on accordion availability
  Widget _buildConditionalAddIcon({
    required VoidCallback onTap,
    required String tooltipMessage,
  }) {
    return Consumer<AccordionAvailabilityProvider>(
      builder: (context, accordionProvider, child) {
        final isEnabled = true;
        // accordionProvider.canAddToAccordion;
        final iconColor = isEnabled ? Colors.grey : Colors.grey.shade400;
        final cursor =
            isEnabled ? SystemMouseCursors.click : SystemMouseCursors.forbidden;

        return MouseRegion(
          cursor: cursor,
          child: Tooltip(
            message: isEnabled
                ? tooltipMessage
                : accordionProvider.getDisabledMessage(),
            child: GestureDetector(
              onTap: isEnabled
                  ? onTap
                  : () => _showDisabledMessage(
                      context, accordionProvider.getDisabledMessage()),
              child: Icon(
                Icons.add,
                size: 16,
                color: iconColor,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Show message when add action is disabled
  void _showDisabledMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Handle GO selection - populate data in the right panel
  void _handleGoSelection(BuildContext context, Map<String, dynamic> go) {
    final goDetailsProvider = Provider.of<GoDetailsProvider>(context, listen: false);

    // Ensure local objectives are loaded before populating
    if (!go['localObjectivesLoaded']) {
      // If local objectives are not loaded, load them first
      final goId = go['goId'];
      if (goId != null && goId.isNotEmpty) {
        _fetchGoLocalObjectives(goId, _goData.indexOf(go)).then((_) {
          // After loading, populate the GO data
          goDetailsProvider.populateFromGoSelection(go);
        });
      }
    } else {
      // Local objectives are already loaded, populate directly
      goDetailsProvider.populateFromGoSelection(go);
    }

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'GO "${go['name']}" loaded successfully',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Fetch attributes for a specific entity from cached library data
  Future<void> _fetchEntityAttributes(String entityId, int objectIndex) async {
    if (_entityAttributes.containsKey(entityId)) {
      // Use cached attributes
      setState(() {
        _objectsData[objectIndex]['attributes'] = _entityAttributes[entityId]!;
        _objectsData[objectIndex]['attributesLoaded'] = true;
      });
      return;
    }

    setState(() {
      _isLoadingAttributes = true;
    });

    try {
      List<String> attributeNames = [];

      // Use cached library data if available
      if (_cachedLibraryData?.entityAttributes != null) {
        final allAttributes = <EntityAttributesMongoDraft>[];

        // Add postgres attributes
        if (_cachedLibraryData!.entityAttributes!.postgres != null) {
          allAttributes.addAll(_cachedLibraryData!.entityAttributes!.postgres!);
        }

        // Add mongo draft attributes
        if (_cachedLibraryData!.entityAttributes!.mongoDrafts != null) {
          allAttributes
              .addAll(_cachedLibraryData!.entityAttributes!.mongoDrafts!);
        }

        // Filter attributes for this entity
        final entityAttributes =
            allAttributes.where((attr) => attr.entityId == entityId).toList();

        attributeNames = entityAttributes
            .map((attr) => attr.displayName?.isNotEmpty == true
                ? attr.displayName!
                : attr.name ?? 'Unknown Attribute')
            .toList();
      }

      // Cache the attributes
      _entityAttributes[entityId] = attributeNames;

      setState(() {
        _objectsData[objectIndex]['attributes'] = attributeNames;
        _objectsData[objectIndex]['attributesLoaded'] = true;
        _isLoadingAttributes = false;
      });

      print(
          'Loaded ${attributeNames.length} attributes for entity $entityId from cached data');
    } catch (e) {
      setState(() {
        _objectsData[objectIndex]['attributes'] = <String>[];
        _objectsData[objectIndex]['attributesLoaded'] = true;
        _isLoadingAttributes = false;
      });
      print('Error fetching attributes for entity $entityId: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //  width: 300,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 1),
          right: BorderSide(color: Colors.grey.shade300, width: 1),
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
          left: BorderSide.none, // or BorderSide(width: 0)
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          _buildTabBar(),
          _buildSearchRow(),
          Expanded(
            child: _buildTabContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        onTap: (value) {
          Provider.of<CreationProvider>(context, listen: false)
              .currentMiddleScreen = value;
          Provider.of<CreationProvider>(context, listen: false)
              .isGoMyLibraryClicked = false;
        },
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey.shade600,
        indicator: BoxDecoration(
          color: Colors.black,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.grey.shade300, // Set divider color to grey
        labelStyle: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.medium,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.white,
        ),
        unselectedLabelStyle: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.regular,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.grey.shade600,
        ),
        tabs: const [
          Tab(text: 'Roles'),
          Tab(text: 'Objects'),
          Tab(text: 'GO'),
        ],
      ),
    );
  }

  Widget _buildSearchRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Row(
        children: [
          // Search TextField
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey.shade500,
                ),
                border: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: AppColors.primaryBlue, width: 2),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                isDense: true,
                filled: false,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
              ),
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          // Spacer (10px)
          const SizedBox(width: 10),

          // Dropdown
          Expanded(
            flex: 1,
            child: Container(
              height: 28,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
                borderRadius: BorderRadius.circular(0),
                color: Colors.white,
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedFilter,
                  isExpanded: true,
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    size: 18,
                    color: Colors.grey.shade600,
                  ),
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  dropdownColor: Colors.white,
                  items: ['All', 'Draft', 'Published']
                      .map((item) => DropdownMenuItem(
                            value: item,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Text(item),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedFilter = value;
                      });
                    }
                  },
                  selectedItemBuilder: (BuildContext context) {
                    return ['All', 'Draft', 'Publish']
                        .map<Widget>((String item) {
                      return Container(
                        alignment: Alignment.centerLeft,
                        padding: const EdgeInsets.symmetric(horizontal: 0),
                        child: Text(
                          item,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildRolesTab(),
        _buildObjectsTab(),
        _buildGoTab(),
      ],
    );
  }

  Widget _buildRolesTab() {
    if (_isLoadingRoles) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final filteredRoles = _rolesData.where((role) {
      return _searchQuery.isEmpty || role.toLowerCase().contains(_searchQuery);
    }).toList();

    if (filteredRoles.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'No roles found for "$_searchQuery"',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredRoles.isEmpty) {
      return Center(
        child: Text(
          'No roles available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
      itemCount: filteredRoles.length,
      itemBuilder: (context, index) {
        final role = filteredRoles[index];
        return _buildRoleItem(role);
      },
    );
  }

  Widget _buildObjectsTab() {
    if (_isLoadingEntities) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final filteredObjects = _objectsData.where((object) {
      // Apply search filter
      bool matchesSearch = _searchQuery.isEmpty ||
          object['displayName'].toString().toLowerCase().contains(_searchQuery);

      // Apply status filter
      bool matchesStatus = true;
      if (_selectedFilter == 'Draft') {
        matchesStatus = object['status'] == 'draft';
      } else if (_selectedFilter == 'Published') {
        matchesStatus = object['status'] == 'published';
      }
      // 'All' shows both draft and published

      return matchesSearch && matchesStatus;
    }).toList();

    if (filteredObjects.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'No objects found for "$_searchQuery"',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredObjects.isEmpty && _selectedFilter != 'All') {
      return Center(
        child: Text(
          'No ${_selectedFilter.toLowerCase()} entities available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredObjects.isEmpty) {
      return Center(
        child: Text(
          'No entities available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(
        horizontal: 0, // Remove horizontal padding for full width
        vertical: 6,
      ),
      itemCount: filteredObjects.length,
      itemBuilder: (context, index) {
        final object = filteredObjects[index];
        final originalIndex = _objectsData.indexOf(object);
        return _buildObjectExpansionTile(object, originalIndex);
      },
    );
  }

  Widget _buildGoTab() {
    if (_isLoadingGO) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final filteredGOs = _goData.where((go) {
      return _searchQuery.isEmpty ||
          go['name'].toString().toLowerCase().contains(_searchQuery);
    }).toList();

    if (filteredGOs.isEmpty && _searchQuery.isNotEmpty) {
      return Center(
        child: Text(
          'No GOs found for "$_searchQuery"',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    if (filteredGOs.isEmpty) {
      return Center(
        child: Text(
          'No Global Objectives available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
      itemCount: filteredGOs.length,
      itemBuilder: (context, index) {
        final go = filteredGOs[index];
        final originalIndex = _goData.indexOf(go);
        return _buildGoExpansionTile(go, originalIndex);
      },
    );
  }

  Widget _buildRoleItem(String role) {
    return Container(
      margin: const EdgeInsets.only(bottom: 0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        color: Colors.white,
      ),
      child: ListTile(
        title: Text(
          role,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        trailing: MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              print('Role plus button tapped: $role');
            },
            child: const Icon(
              Icons.add,
              size: 16,
              color: Colors.grey,
            ),
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        minVerticalPadding: 0,
        dense: true,
      ),
    );
  }

  Widget _buildGoExpansionTile(Map<String, dynamic> go, int index) {
    final bool isExpanded = go['isExpanded'];

    return Container(
      margin: const EdgeInsets.only(bottom: 0), // No spacing between items
      decoration: BoxDecoration(
        border: isExpanded
            ? Border.all(
                color: AppColors.primaryBlue,
                width: 2,
              )
            : Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
        borderRadius: isExpanded ? BorderRadius.circular(4) : BorderRadius.zero,
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent, // Remove default divider
        ),
        child: ExpansionTile(
          title: Text(
            'GO: ${go['name']}',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.medium,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Plus button with cursor pointer on hover
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    // Handle plus button tap - populate GO data in the right panel
                    print('GO plus button tapped: ${go['name']}');
                    _handleGoSelection(context, go);
                  },
                  child: const Icon(
                    Icons.add,
                    size: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
              const SizedBox(width: 8), // Reduced spacing
              // Expansion arrow
              Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey.shade600,
                size: 20,
              ),
            ],
          ),
          initiallyExpanded: false, // Always start collapsed
          onExpansionChanged: (expanded) async {
            setState(() {
              // Close all other GO panels when opening this one
              if (expanded) {
                for (int i = 0; i < _goData.length; i++) {
                  if (i != index) {
                    _goData[i]['isExpanded'] = false;
                  }
                }
              }
              _goData[index]['isExpanded'] = expanded;
            });

            // Fetch local objectives when expanding and not already loaded
            if (expanded && !go['localObjectivesLoaded']) {
              final goId = go['goId'];
              if (goId != null && goId.isNotEmpty) {
                print('Expanding GO: ${go['name']} with ID: $goId');
                await _fetchGoLocalObjectives(goId, index);
              }
            }
          },
          tilePadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 0, // Zero vertical padding to achieve 36px height
          ),
          childrenPadding: EdgeInsets
              .zero, // Remove all padding for full width local objectives
          children: [
            // Local objectives list with full width, plus icons, and bottom borders
            ...go['localObjectives'].asMap().entries.map<Widget>((entry) {
              final int loIndex = entry.key;
              final String localObjective = entry.value;
              final bool isLastLO = loIndex == go['localObjectives'].length - 1;

              return Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: isLastLO
                        ? BorderSide.none
                        : BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12, // Only horizontal padding to match container
                  vertical: 6, // Proper vertical padding for better spacing
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        localObjective,
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                    // Plus icon with cursor pointer on hover
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          // Handle local objective plus button tap
                          print(
                              'Local Objective plus button tapped: $localObjective');
                        },
                        child: const Icon(
                          Icons.add,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectExpansionTile(Map<String, dynamic> object, int index) {
    final bool isExpanded = object['isExpanded'];

    return Container(
      margin: const EdgeInsets.only(bottom: 0), // No spacing between items
      decoration: BoxDecoration(
        border: isExpanded
            ? Border.all(
                color: AppColors.primaryBlue,
                width: 2,
              )
            : Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
        borderRadius: isExpanded ? BorderRadius.circular(4) : BorderRadius.zero,
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent, // Remove default divider
        ),
        child: ExpansionTile(
          title: Row(
            children: [
              Expanded(
                child: Tooltip(
                  message:
                      'Object: ${object['displayName'] != null && object['displayName'].isNotEmpty ? object['displayName'] : object['name']}',
                  child: Text(
                    'Object: ${object['displayName'] != null && object['displayName'].isNotEmpty ? object['displayName'] : object['name']}',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Status indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: object['status'] == 'draft'
                      ? Colors.orange.shade100
                      : Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: object['status'] == 'draft'
                        ? Colors.orange.shade300
                        : Colors.green.shade300,
                    width: 1,
                  ),
                ),
                child: Text(
                  object['status'] == 'draft' ? 'Draft' : 'Published',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s10,
                    fontWeight: FontManager.medium,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: object['status'] == 'draft'
                        ? Colors.orange.shade700
                        : Colors.green.shade700,
                  ),
                ),
              ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Plus button with conditional enabling
              _buildConditionalAddIcon(
                onTap: () {
                  print('Object plus button tapped: ${object['name']}');
                  _handleObjectSelection(context, object);
                },
                tooltipMessage:
                    'Add ${object['displayName'] ?? object['name']} to Input Stack',
              ),
              const SizedBox(width: 8), // Reduced spacing
              // Expansion arrow
              Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey.shade600,
                size: 20,
              ),
            ],
          ),
          initiallyExpanded: false, // Always start collapsed
          onExpansionChanged: (expanded) async {
            setState(() {
              // Close all other Object panels when opening this one
              if (expanded) {
                for (int i = 0; i < _objectsData.length; i++) {
                  if (i != index) {
                    _objectsData[i]['isExpanded'] = false;
                  }
                }
              }
              _objectsData[index]['isExpanded'] = expanded;
            });

            // Fetch attributes when expanding and not already loaded
            if (expanded && !object['attributesLoaded']) {
              final entityId = object['entityId'];
              if (entityId != null && entityId.isNotEmpty) {
                await _fetchEntityAttributes(entityId, index);
              }
            }
          },
          tilePadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 0, // Zero vertical padding to achieve 36px height
          ),
          childrenPadding:
              EdgeInsets.zero, // Remove all padding for full width attributes
          children: [
            // Attributes list with full width, plus icons, and bottom borders
            ...object['attributes'].asMap().entries.map<Widget>((entry) {
              final int attributeIndex = entry.key;
              final String attribute = entry.value;
              final bool isLastAttribute =
                  attributeIndex == object['attributes'].length - 1;

              return Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: isLastAttribute
                        ? BorderSide.none
                        : BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12, // Only horizontal padding to match container
                  vertical: 6, // Proper vertical padding for better spacing
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        attribute,
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                    // Plus icon with conditional enabling
                    _buildConditionalAddIcon(
                      onTap: () {
                        print('Attribute plus button tapped: $attribute');
                        _handleAttributeSelection(context, object, attribute);
                      },
                      tooltipMessage: 'Add $attribute to Input Stack',
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// Handle object selection and populate accordion
  void _handleObjectSelection(
      BuildContext context, Map<String, dynamic> object) async {
    // // Check if accordion is available before proceeding
    // try {
    //   final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
    //     context,
    //     listen: false,
    //   );
    //   if (!accordionProvider.canAddToAccordion) {
    //     _showDisabledMessage(context, accordionProvider.getDisabledMessage());
    //     return;
    //   }
    // } catch (e) {
    //   // Provider might not be available in some contexts - continue anyway
    // }

    final provider = Provider.of<CreationProvider>(context, listen: false);
    if (provider.currentMiddleScreen == 2 || provider.isGoMyLibraryClicked) {
      // Use GoDetailsProvider for LO-specific accordion state management
      final goDetailsProvider =
          Provider.of<GoDetailsProvider>(context, listen: false);
      final selectedLoIndex = goDetailsProvider.selectedLocalObjectiveIndex;

      print('=== Object Selection Debug ===');
      print('Object name: ${object['name']}');
      print('Object displayName: ${object['displayName']}');
      print('Object entityId: ${object['entityId']}');
      print('Initial attributes: ${object['attributes']}');
      print('Attributes loaded: ${object['attributesLoaded']}');

      // Get the object's attributes - if not loaded, fetch them first
      List<String> attributes = List<String>.from(object['attributes'] ?? []);

      // If attributes are empty and not loaded, fetch them
      if (attributes.isEmpty && !object['attributesLoaded']) {
        print('Attributes not loaded, fetching...');
        final entityId = object['entityId'];
        if (entityId != null && entityId.isNotEmpty) {
          // Find the object index to update the correct object
          final objectIndex = _objectsData.indexOf(object);
          if (objectIndex != -1) {
            print('Found object at index: $objectIndex');
            // Fetch attributes for this entity
            await _fetchEntityAttributes(entityId, objectIndex);
            // Get the updated attributes after fetching
            attributes = List<String>.from(
                _objectsData[objectIndex]['attributes'] ?? []);
            print('Fetched attributes: $attributes');
          } else {
            print('Object not found in _objectsData');
          }
        } else {
          print('No entityId found for object');
        }
      } else {
        print('Using existing attributes: $attributes');
      }

      // Add the selected object to the appropriate accordion state
      if (selectedLoIndex != null) {
        // Add to LO-specific accordion state
        goDetailsProvider.addLoSelectedObject(
            selectedLoIndex, object, attributes);
        print('Added object to LO-${selectedLoIndex + 1} accordion');
      } else {
        // Fall back to global provider for backward compatibility
        final selectedObjectProvider =
            Provider.of<SelectedObjectProvider>(context, listen: false);
        selectedObjectProvider.addSelectedObject(object, attributes);
        print('Added object to global accordion (no LO selected)');
      }

      // Show a confirmation message (check if widget is still mounted)
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              selectedLoIndex != null
                  ? 'Added ${object['displayName'] ?? object['name']} to LO-${selectedLoIndex + 1} with ${attributes.length} attributes'
                  : 'Selected ${object['displayName'] ?? object['name']} with ${attributes.length} attributes',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } else if (provider.currentMiddleScreen == 1) {
      Provider.of<CreateEntityProvider>(context, listen: false)
          .handleMyLibraryObject(ObjectCreationModel(
        id: object['entityId'],
        name: object['name'],
        displayName: object['displayName'],
        // description: object['description'],
        // type: object['type'],
      ));
    }
  }

  /// Handle single attribute selection and populate accordion
  void _handleAttributeSelection(
      BuildContext context, Map<String, dynamic> object, String attribute) {
    // Check if accordion is available before proceeding
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      if (!accordionProvider.canAddToAccordion) {
        _showDisabledMessage(context, accordionProvider.getDisabledMessage());
        return;
      }
    } catch (e) {
      // Provider might not be available in some contexts - continue anyway
    }

    // Use GoDetailsProvider for LO-specific accordion state management
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);
    final selectedLoIndex = goDetailsProvider.selectedLocalObjectiveIndex;

    // Create a modified object with just the selected attribute
    final modifiedObject = Map<String, dynamic>.from(object);
    modifiedObject['attributes'] = [attribute];

    // Create a unique identifier for single attribute selections
    // Use a combination of object name and attribute name to make it unique
    final objectName = object['displayName'] ?? object['name'] ?? 'Unknown';
    modifiedObject['displayName'] = '$objectName - $attribute';
    modifiedObject['name'] = '$objectName - $attribute';

    // Generate a unique entityId for this attribute selection if one doesn't exist
    if (modifiedObject['entityId'] == null ||
        modifiedObject['entityId'].isEmpty) {
      modifiedObject['entityId'] =
          '${object['entityId'] ?? objectName}_${attribute}_attr';
    } else {
      modifiedObject['entityId'] =
          '${modifiedObject['entityId']}_${attribute}_attr';
    }

    // Add the selected attribute to the appropriate accordion state
    if (selectedLoIndex != null) {
      // Add to LO-specific accordion state
      goDetailsProvider
          .addLoSelectedObject(selectedLoIndex, modifiedObject, [attribute]);
      print('Added attribute to LO-${selectedLoIndex + 1} accordion');
    } else {
      // Fall back to global provider for backward compatibility
      final selectedObjectProvider =
          Provider.of<SelectedObjectProvider>(context, listen: false);
      selectedObjectProvider.addSelectedObject(modifiedObject, [attribute]);
      print('Added attribute to global accordion (no LO selected)');
    }

    // Show a confirmation message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          selectedLoIndex != null
              ? 'Added attribute "$attribute" to LO-${selectedLoIndex + 1} from ${object['displayName'] ?? object['name']}'
              : 'Added attribute "$attribute" from ${object['displayName'] ?? object['name']}',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
