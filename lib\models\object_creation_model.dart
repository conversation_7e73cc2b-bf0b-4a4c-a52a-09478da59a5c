import 'dart:convert';

import 'package:flutter/material.dart';

/// Model for object creation/extraction entities
class ObjectCreationModel {
  final String? tenant;
  final String? entityDeclaration;
  String? id;
  String? name;
  String? displayName;
  String? type;
  String? description;
  String? businessPurpose;
  String? businessDomain;
  String? category;
  String? archivalStrategy;
  String? colorTheme;
  String? icon;
  bool isEntityValidatedSaved;
  List<String>? tags;
  bool isEntityAttributesValidatedSaved;
  bool isEntityRelationshipsValidatedSaved;
  bool isEntityBusinessRulesValidatedSaved;
  bool isEntityEnumValuesValidatedSaved;
  bool isEntitySecurityClassificationValidatedSaved;
  bool isEntitySystemPropertiesValidatedSaved;
  bool attributesExpanded;
  bool relationshipsExpanded;
  bool enumValuesExpanded;

  List<ObjectAttribute>? attributes;
  List<EntityRelationship>? relationships;
  final List<BusinessRule>? businessRules;
  List<EnumValue>? enumValues;
  final List<SecurityClassification>? securityClassification;
  final List<SystemPermission>? systemPermissions;
  final List<RoleSystemPermission>? roleSystemPermissions;
  final List<UiProperty>? uiProperties;
  final double? confidence;
  final String? extractionMethod;
  final int? completionScore;
  final ConfigurationStatus? configurationStatus;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ObjectCreationModel({
    this.tenant,
    this.entityDeclaration,
    this.id,
    this.name,
    this.displayName,
    this.type,
    this.description,
    this.businessPurpose,
    this.businessDomain,
    this.category,
    this.archivalStrategy,
    this.colorTheme,
    this.icon,
    this.tags,
    this.attributes,
    this.relationships,
    this.businessRules,
    this.enumValues,
    this.securityClassification,
    this.systemPermissions,
    this.roleSystemPermissions,
    this.uiProperties,
    this.confidence,
    this.extractionMethod,
    this.completionScore,
    this.configurationStatus,
    this.createdAt,
    this.updatedAt,
    this.isEntityValidatedSaved = false,
    this.isEntityAttributesValidatedSaved = false,
    this.isEntityRelationshipsValidatedSaved = false,
    this.isEntityBusinessRulesValidatedSaved = false,
    this.isEntityEnumValuesValidatedSaved = false,
    this.isEntitySecurityClassificationValidatedSaved = false,
    this.isEntitySystemPropertiesValidatedSaved = false,
    this.attributesExpanded = false,
    this.relationshipsExpanded = false,
    this.enumValuesExpanded = false,
  });

  /// Enhanced factory method with better error handling and fallback support
  factory ObjectCreationModel.fromJson(Map<String, dynamic> json) {
    try {
      return ObjectCreationModel(
        tenant: json['tenant']?.toString(),
        entityDeclaration: json['entityDeclaration']?.toString(),
        id: json['id']?.toString(),
        name: json['name']?.toString() ?? json['objectName']?.toString(),
        displayName: json['displayName']?.toString() ??
            json['name']?.toString() ??
            json['objectName']?.toString(),
        type: json['type']?.toString(),
        description: json['description']?.toString(),
        businessPurpose: json['businessPurpose']?.toString(),
        businessDomain: json['businessDomain']?.toString(),
        category: json['category']?.toString(),
        archivalStrategy: json['archivalStrategy']?.toString(),
        colorTheme: json['colorTheme']?.toString(),
        icon: json['icon']?.toString(),
        tags: _safeParseStringList(json['tags']),
        attributes: _safeParseList<ObjectAttribute>(
            json['attributes'], (item) => ObjectAttribute.fromJson(item)),
        relationships: _safeParseList<EntityRelationship>(
            json['relationships'], (item) => EntityRelationship.fromJson(item)),
        businessRules: _safeParseList<BusinessRule>(
            json['business_rules'] ?? json['businessRules'],
            (item) => BusinessRule.fromJson(item)),
        enumValues: _safeParseList<EnumValue>(
            json['enum_values'] ?? json['enumValues'],
            (item) => EnumValue.fromJson(item)),
        securityClassification: _safeParseList<SecurityClassification>(
            json['security_classification'] ?? json['securityClassification'],
            (item) => SecurityClassification.fromJson(item)),
        systemPermissions: _safeParseList<SystemPermission>(
            json['system_permissions'] ?? json['systemPermissions'],
            (item) => SystemPermission.fromJson(item)),
        roleSystemPermissions: _safeParseList<RoleSystemPermission>(
            json['role_system_permissions'] ?? json['roleSystemPermissions'],
            (item) => RoleSystemPermission.fromJson(item)),
        uiProperties: json['uiProperties'] != null
            ? (json['uiProperties'] as List)
                .map((e) => UiProperty.fromJson(e as Map<String, dynamic>))
                .toList()
            : null,
        confidence: _safeParseDouble(json['confidence']),
        extractionMethod: json['extraction_method']?.toString() ??
            json['extractionMethod']?.toString(),
        completionScore:
            _safeParseInt(json['completion_score'] ?? json['completionScore']),
        configurationStatus: json['configuration_status'] != null
            ? ConfigurationStatus.fromJson(json['configuration_status'])
            : json['configurationStatus'] != null
                ? ConfigurationStatus.fromJson(json['configurationStatus'])
                : null,
        createdAt: _safeParseDateTime(json['created_at'] ?? json['createdAt']),
        updatedAt: _safeParseDateTime(json['updated_at'] ?? json['updatedAt']),
      );
    } catch (e) {
      // If parsing fails, rethrow the error instead of creating fallback object
      throw Exception('Failed to parse ObjectCreationModel from JSON: $e');
    }
  }

  /// Safe string list parsing
  static List<String>? _safeParseStringList(dynamic value) {
    try {
      if (value == null) return null;
      if (value is List) {
        return value.map((item) => item.toString()).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe list parsing with transformation
  static List<T>? _safeParseList<T>(
      dynamic value, T Function(dynamic) transform) {
    try {
      if (value == null) return null;
      if (value is List) {
        final List<T> result = [];
        for (final item in value) {
          try {
            result.add(transform(item));
          } catch (e) {
            // Skip invalid items but continue processing
            continue;
          }
        }
        return result.isEmpty ? null : result;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe double parsing
  static double? _safeParseDouble(dynamic value) {
    try {
      if (value == null) return null;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe int parsing
  static int? _safeParseInt(dynamic value) {
    try {
      if (value == null) return null;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe DateTime parsing
  static DateTime? _safeParseDateTime(dynamic value) {
    try {
      if (value == null) return null;
      if (value is String) return DateTime.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'tenant': tenant,
      'entityDeclaration': entityDeclaration,
      'id': id,
      'name': name,
      'displayName': displayName,
      'type': type,
      'description': description,
      'businessPurpose': businessPurpose,
      'businessDomain': businessDomain,
      'category': category,
      'archivalStrategy': archivalStrategy,
      'colorTheme': colorTheme,
      'icon': icon,
      'tags': tags,
      'attributes': attributes?.map((attr) => attr.toJson()).toList(),
      'relationships': relationships?.map((rel) => rel.toJson()).toList(),
      'business_rules': businessRules?.map((rule) => rule.toJson()).toList(),
      'enum_values': enumValues?.map((enumVal) => enumVal.toJson()).toList(),
      'security_classification':
          securityClassification?.map((sec) => sec.toJson()).toList(),
      'system_permissions':
          systemPermissions?.map((perm) => perm.toJson()).toList(),
      'role_system_permissions':
          roleSystemPermissions?.map((rolePerm) => rolePerm.toJson()).toList(),
      'uiProperties': uiProperties?.map((e) => e.toJson()).toList(),
      'confidence': confidence,
      'extraction_method': extractionMethod,
      'completion_score': completionScore,
      'configuration_status': configurationStatus?.toJson(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'ObjectCreationModel(id: $id, name: $name, displayName: $displayName, type: $type)';
  }
}

/// Model for object attributes
class ObjectAttribute {
  final String? name;
  final String? displayName;
  final String? dataType;
  final bool? required;
  final bool? unique;
  final String? defaultType;
  final String? defaultValue;
  final String? description;
  final String? helperText;
  final List<String>? enumValues;
  final ValidationRule? validation;
  final bool isPrimaryKey;
  final bool isForeignKey;

  ObjectAttribute({
    this.name,
    this.displayName,
    this.dataType,
    this.required,
    this.unique,
    this.defaultType,
    this.defaultValue,
    this.description,
    this.helperText,
    this.enumValues,
    this.validation,
    this.isPrimaryKey = false,
    this.isForeignKey = false,
  });

  factory ObjectAttribute.fromJson(Map<String, dynamic> json) {
    return ObjectAttribute(
      name: json['name']?.toString(),
      displayName: json['displayName']?.toString(),
      dataType: json['dataType']?.toString(),
      required: json['required'] is bool ? json['required'] : false,
      unique: json['unique'] is bool ? json['unique'] : false,
      defaultType: json['defaultType']?.toString(),
      defaultValue: json['defaultValue']?.toString(),
      description: json['description']?.toString(),
      helperText: json['helperText']?.toString(),
      enumValues: json['enumValues'] != null
          ? List<String>.from(json['enumValues'])
          : null,
      validation: json['validation'] != null
          ? ValidationRule.fromJson(json['validation'])
          : null,
      isPrimaryKey: json['isPrimaryKey'] as bool? ?? false,
      isForeignKey: json['isForeignKey'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'dataType': dataType,
      'required': required,
      'unique': unique,
      'defaultType': defaultType,
      'defaultValue': defaultValue,
      'description': description,
      'helperText': helperText,
      'enumValues': enumValues,
      'validation': validation?.toJson(),
      'isPrimaryKey': isPrimaryKey,
      'isForeignKey': isForeignKey,
    };
  }
}

/// Model for entity relationships
class EntityRelationship {
  final String? primaryEntity;
  final String? primaryKey;
  final String? relatedEntity;
  final String? relationshipType;
  final String? onDelete;
  final String? onUpdate;
  final String? foreignKey;
  final String? foreignKeyType;
  final String? joinTable;
  final String? description;
  final ScrollController scrollController;

  EntityRelationship({
    this.primaryEntity,
    this.primaryKey,
    this.relatedEntity,
    this.relationshipType,
    this.onDelete,
    this.onUpdate,
    this.foreignKey,
    this.foreignKeyType,
    this.joinTable,
    this.description,
    ScrollController? scrollController, // optional
  }) : scrollController = scrollController ?? ScrollController();

  factory EntityRelationship.fromJson(Map<String, dynamic> json) {
    return EntityRelationship(
      primaryEntity: json['primaryEntity']?.toString(),
      primaryKey: json['primaryKey']?.toString(),
      relatedEntity: json['relatedEntity']?.toString(),
      relationshipType: json['relationshipType']?.toString(),
      onDelete: json['onDelete']?.toString(),
      onUpdate: json['onUpdate']?.toString(),
      foreignKey: json['foreignKey']?.toString(),
      foreignKeyType: json['foreignKeyType']?.toString(),
      joinTable: json['joinTable']?.toString(),
      description: json['description']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'primaryEntity': primaryEntity,
      'primaryKey': primaryKey,
      'relatedEntity': relatedEntity,
      'relationshipType': relationshipType,
      'onDelete': onDelete,
      'onUpdate': onUpdate,
      'foreignKey': foreignKey,
      'joinTable': joinTable,
      'description': description,
    };
  }
}

/// Model for business rules
class BusinessRule {
  final String? attributeName;
  final String? entityName;
  final String? operator;
  final String? pattern;
  final String? errorMessage;
  final double? minValue;
  final double? maxValue;
  final String? leftOperand;
  final String? rightOperand;
  final String? successValueRange;
  final String? warningValueRange;
  final String? failureValueRange;
  final String? multiConditionOperator;
  final String? warningMessage;
  final String? successMessage;

  BusinessRule({
    this.attributeName,
    this.entityName,
    this.operator,
    this.pattern,
    this.errorMessage,
    this.minValue,
    this.maxValue,
    this.leftOperand,
    this.rightOperand,
    this.successValueRange,
    this.warningValueRange,
    this.failureValueRange,
    this.multiConditionOperator,
    this.warningMessage,
    this.successMessage,
  });

  factory BusinessRule.fromJson(Map<String, dynamic> json) {
    return BusinessRule(
      attributeName: json['attributeName']?.toString(),
      entityName: json['entityName']?.toString(),
      operator: json['operator']?.toString(),
      pattern: json['pattern']?.toString(),
      errorMessage: json['errorMessage']?.toString(),
      minValue: json['minValue']?.toDouble(),
      maxValue: json['maxValue']?.toDouble(),
      leftOperand: json['leftOperand']?.toString(),
      rightOperand: json['rightOperand']?.toString(),
      successValueRange: json['successValueRange']?.toString(),
      warningValueRange: json['warningValueRange']?.toString(),
      failureValueRange: json['failureValueRange']?.toString(),
      multiConditionOperator: json['multiConditionOperator']?.toString(),
      warningMessage: json['warningMessage']?.toString(),
      successMessage: json['successMessage']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attributeName': attributeName,
      'entityName': entityName,
      'operator': operator,
      'pattern': pattern,
      'errorMessage': errorMessage,
      'minValue': minValue,
      'maxValue': maxValue,
      'leftOperand': leftOperand,
      'rightOperand': rightOperand,
      'successValueRange': successValueRange,
      'warningValueRange': warningValueRange,
      'failureValueRange': failureValueRange,
      'multiConditionOperator': multiConditionOperator,
      'warningMessage': warningMessage,
      'successMessage': successMessage,
    };
  }
}

/// Model for enum values
class EnumValue {
  final String? entityAttribute;
  final String? enumName;
  final String? value;
  final String? display;
  final String? description;
  final int? sortOrder;
  final bool? active;

  EnumValue({
    this.entityAttribute,
    this.enumName,
    this.value,
    this.display,
    this.description,
    this.sortOrder,
    this.active,
  });

  factory EnumValue.fromJson(Map<String, dynamic> json) {
    return EnumValue(
      entityAttribute: json['entityAttribute']?.toString(),
      enumName: json['enumName']?.toString(),
      value: json['value']?.toString(),
      display: json['display']?.toString(),
      description: json['description']?.toString(),
      sortOrder: json['sortOrder']?.toInt(),
      active: json['active'] is bool ? json['active'] : false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entityAttribute': entityAttribute,
      'enumName': enumName,
      'value': value,
      'display': display,
      'description': description,
      'sortOrder': sortOrder,
      'active': active,
    };
  }
}

/// Model for security classification
class SecurityClassification {
  final String? entityAttribute;
  final String? classification;
  final String? piiType;
  final bool? encryptionRequired;
  final String? encryptionType;
  final bool? maskingRequired;
  final String? maskingPattern;
  final String? accessLevel;
  final bool? auditTrail;
  final String? dataResidency;
  final String? retentionOverride;
  final String? anonymizationRequired;
  final String? anonymizationMethod;
  final String? anonymizationPattern;
  final List? complianceFrameworks;
  final List<String>? actions;

  SecurityClassification({
    this.entityAttribute,
    this.classification,
    this.piiType,
    this.encryptionRequired,
    this.encryptionType,
    this.maskingRequired,
    this.maskingPattern,
    this.accessLevel,
    this.auditTrail,
    this.dataResidency,
    this.retentionOverride,
    this.anonymizationRequired,
    this.anonymizationMethod,
    this.anonymizationPattern,
    this.complianceFrameworks,
    this.actions,
  });

  factory SecurityClassification.fromJson(Map<String, dynamic> json) {
    return SecurityClassification(
      entityAttribute: json['entityAttribute']?.toString(),
      classification: json['classification']?.toString(),
      piiType: json['piiType']?.toString(),
      encryptionRequired: json['encryptionRequired'] is bool
          ? json['encryptionRequired']
          : false,
      encryptionType: json['encryptionType']?.toString(),
      maskingRequired:
          json['maskingRequired'] is bool ? json['maskingRequired'] : false,
      maskingPattern: json['maskingPattern']?.toString(),
      accessLevel: json['accessLevel']?.toString(),
      auditTrail: json['auditTrail'] is bool ? json['auditTrail'] : false,
      dataResidency: json['dataResidency']?.toString(),
      retentionOverride: json['retentionOverride']?.toString(),
      anonymizationRequired: json['anonymizationRequired'] is bool
          ? json['anonymizationRequired']
          : false,
      anonymizationMethod: json['anonymizationMethod']?.toString(),
      anonymizationPattern: json['anonymizationPattern']?.toString(),
      complianceFrameworks: json['complianceFrameworks'] != null
          ? List<dynamic>.from(json['complianceFrameworks'])
          : null,
      actions:
          json['actions'] != null ? List<String>.from(json['actions']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entityAttribute': entityAttribute,
      'classification': classification,
      'piiType': piiType,
      'encryptionRequired': encryptionRequired,
      'encryptionType': encryptionType,
      'maskingRequired': maskingRequired,
      'maskingPattern': maskingPattern,
      'accessLevel': accessLevel,
      'auditTrail': auditTrail,
      'dataResidency': dataResidency,
      'retentionOverride': retentionOverride,
      'anonymizationRequired': anonymizationRequired,
      'anonymizationMethod': anonymizationMethod,
      'anonymizationPattern': anonymizationPattern,
      'complianceFrameworks': complianceFrameworks,
      'actions': actions,
    };
  }
}

/// Model for system permissions
class SystemPermission {
  final String? permissionId;
  final String? permissionName;
  final String? permissionType;
  final String? resourceIdentifier;
  final List<String>? actions;
  final String? description;
  final String? scope;
  final String? naturalLanguage;
  final int? version;
  final String? status;

  SystemPermission({
    this.permissionId,
    this.permissionName,
    this.permissionType,
    this.resourceIdentifier,
    this.actions,
    this.description,
    this.scope,
    this.naturalLanguage,
    this.version,
    this.status,
  });

  factory SystemPermission.fromJson(Map<String, dynamic> json) {
    return SystemPermission(
      permissionId: json['permissionId']?.toString(),
      permissionName: json['permissionName']?.toString(),
      permissionType: json['permissionType']?.toString(),
      resourceIdentifier: json['resourceIdentifier']?.toString(),
      actions:
          json['actions'] != null ? List<String>.from(json['actions']) : null,
      description: json['description']?.toString(),
      scope: json['scope']?.toString(),
      naturalLanguage: json['naturalLanguage']?.toString(),
      version: json['version']?.toInt(),
      status: json['status']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'permissionId': permissionId,
      'permissionName': permissionName,
      'permissionType': permissionType,
      'resourceIdentifier': resourceIdentifier,
      'actions': actions,
      'description': description,
      'scope': scope,
      'naturalLanguage': naturalLanguage,
      'version': version,
      'status': status,
    };
  }
}

/// Model for role system permissions
class RoleSystemPermission {
  final String? roleId;
  final String? permissionId;
  final List<String>? grantedActions;
  final Map<String, dynamic>? rowLevelConditions;
  final String? naturalLanguage;
  final String? version;
  final String? status;

  RoleSystemPermission({
    this.roleId,
    this.permissionId,
    this.grantedActions,
    this.rowLevelConditions,
    this.naturalLanguage,
    this.version,
    this.status,
  });

  factory RoleSystemPermission.fromJson(Map<String, dynamic> json) {
    return RoleSystemPermission(
      roleId: json['roleId']?.toString(),
      permissionId: json['permissionId']?.toString(),
      grantedActions: json['grantedActions'] != null
          ? List<String>.from(json['grantedActions'])
          : null,
      rowLevelConditions: json['rowLevelConditions'] as Map<String, dynamic>?,
      naturalLanguage: json['naturalLanguage']?.toString(),
      version: json['version']?.toString(),
      status: json['status']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roleId': roleId,
      'permissionId': permissionId,
      'grantedActions': grantedActions,
      'rowLevelConditions': rowLevelConditions,
      'naturalLanguage': naturalLanguage,
      'version': version,
      'status': status,
    };
  }
}

/// Model for configuration status
class ConfigurationStatus {
  final String? status;
  final int? completionPercentage;

  ConfigurationStatus({
    this.status,
    this.completionPercentage,
  });

  factory ConfigurationStatus.fromJson(Map<String, dynamic> json) {
    return ConfigurationStatus(
      status: json['status']?.toString(),
      completionPercentage: json['completionPercentage']?.toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'completionPercentage': completionPercentage,
    };
  }
}

/// Model for validation rules
class ValidationRule {
  final bool? required;

  ValidationRule({
    this.required,
  });

  factory ValidationRule.fromJson(Map<String, dynamic> json) {
    return ValidationRule(
      required: json['required'] is bool ? json['required'] : false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'required': required,
    };
  }
}

class UiProperty {
  final String? entityAttribute; // This maps to "Entity.Attribute" in JSON
  final String? controlType;
  final String? displayFormat;
  final String? inputMask;
  final String? placeholderText;
  final bool? autoComplete;
  final bool? readOnly;
  final String? validationDisplay;
  final String? helpTextPosition;
  final String? label;
  final bool? requiredIndicator;

  UiProperty({
    this.entityAttribute,
    this.controlType,
    this.displayFormat,
    this.inputMask,
    this.placeholderText,
    this.autoComplete,
    this.readOnly,
    this.validationDisplay,
    this.helpTextPosition,
    this.label,
    this.requiredIndicator,
  });

  factory UiProperty.fromJson(Map<String, dynamic> json) {
    return UiProperty(
      entityAttribute: json['Entity.Attribute'] as String?,
      controlType: json['controlType'] as String?,
      displayFormat: json['displayFormat'] as String?,
      inputMask: json['inputMask'] as String?,
      placeholderText: json['placeholderText'] as String?,
      autoComplete: json['autoComplete'] as bool?,
      readOnly: json['readOnly'] as bool?,
      validationDisplay: json['validationDisplay'] as String?,
      helpTextPosition: json['helpTextPosition'] as String?,
      label: json['label'] as String?,
      requiredIndicator: json['requiredIndicator'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Entity.Attribute': entityAttribute,
      'controlType': controlType,
      'displayFormat': displayFormat,
      'inputMask': inputMask,
      'placeholderText': placeholderText,
      'autoComplete': autoComplete,
      'readOnly': readOnly,
      'validationDisplay': validationDisplay,
      'helpTextPosition': helpTextPosition,
      'label': label,
      'requiredIndicator': requiredIndicator,
    };
  }
}

/// Response model for session creation API
class SessionCreationResponse {
  final bool success;
  final String? message;
  final String? sessionId;

  SessionCreationResponse({
    required this.success,
    this.message,
    this.sessionId,
  });

  factory SessionCreationResponse.fromJson(Map<String, dynamic> json) {
    try {
      return SessionCreationResponse(
        success: json['success'] ?? false,
        message: json['message']?.toString(),
        sessionId: json['session_id']?.toString(),
      );
    } catch (e) {
      return SessionCreationResponse(
        success: false,
        message: 'Failed to parse session creation response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'session_id': sessionId,
    };
  }
}

/// Response model for comprehensive extraction API
class ComprehensiveExtractionResponse {
  final bool success;
  final String? message;
  final String? sessionId;
  final ComprehensiveExtractionData? data;

  ComprehensiveExtractionResponse({
    required this.success,
    this.message,
    this.sessionId,
    this.data,
  });

  factory ComprehensiveExtractionResponse.fromJson(Map<String, dynamic> json) {
    try {
      return ComprehensiveExtractionResponse(
        success: json['success'] ?? false,
        message: json['message']?.toString(),
        sessionId: json['session_id']?.toString(),
        data: json['data'] != null
            ? ComprehensiveExtractionData.fromJson(json['data'])
            : null,
      );
    } catch (e) {
      return ComprehensiveExtractionResponse(
        success: false,
        message: 'Failed to parse comprehensive extraction response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'session_id': sessionId,
      'data': data?.toJson(),
    };
  }
}

/// Data model for comprehensive extraction response
class ComprehensiveExtractionData {
  final int? totalEntities;
  final int? totalRoles;
  final int? totalGo;
  final int? totalLo;
  final bool? hasExtractions;
  final String? lastUpdated;

  ComprehensiveExtractionData({
    this.totalEntities,
    this.totalRoles,
    this.totalGo,
    this.totalLo,
    this.hasExtractions,
    this.lastUpdated,
  });

  factory ComprehensiveExtractionData.fromJson(Map<String, dynamic> json) {
    return ComprehensiveExtractionData(
      totalEntities: json['total_entities']?.toInt(),
      totalRoles: json['total_roles']?.toInt(),
      totalGo: json['total_go']?.toInt(),
      totalLo: json['total_lo']?.toInt(),
      hasExtractions:
          json['has_extractions'] is bool ? json['has_extractions'] : false,
      lastUpdated: json['last_updated']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_entities': totalEntities,
      'total_roles': totalRoles,
      'total_go': totalGo,
      'total_lo': totalLo,
      'has_extractions': hasExtractions,
      'last_updated': lastUpdated,
    };
  }
}

/// Response model for API calls (backward compatibility)
class ObjectCreationResponse {
  final bool success;
  final String? message;
  final List<ObjectCreationModel>? data;
  final ObjectCreationModel? singleData;
  final int? totalCount;
  final String? sessionId; // Added for new API compatibility

  ObjectCreationResponse({
    required this.success,
    this.message,
    this.data,
    this.singleData,
    this.totalCount,
    this.sessionId,
  });

  /// Enhanced factory method with better error handling
  factory ObjectCreationResponse.fromJson(Map<String, dynamic> json) {
    try {
      return ObjectCreationResponse(
        success: json['success'] ?? false,
        message: json['message']?.toString(),
        data: _safeParseDataList(json['data']),
        singleData: _safeParseSingleData(json['data']),
        totalCount: _safeParseInt(json['totalCount']),
        sessionId: json['session_id']?.toString(),
      );
    } catch (e) {
      // Return error response if parsing fails
      return ObjectCreationResponse(
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  /// Safe parsing of data list
  static List<ObjectCreationModel>? _safeParseDataList(dynamic data) {
    try {
      if (data == null) return null;
      if (data is List) {
        final List<ObjectCreationModel> result = [];
        for (final item in data) {
          try {
            if (item is Map<String, dynamic>) {
              result.add(ObjectCreationModel.fromJson(item));
            }
          } catch (e) {
            // Skip invalid items but continue processing
            continue;
          }
        }
        return result.isEmpty ? null : result;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe parsing of single data object
  static ObjectCreationModel? _safeParseSingleData(dynamic data) {
    try {
      if (data != null && data is Map<String, dynamic>) {
        return ObjectCreationModel.fromJson(data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe int parsing
  static int? _safeParseInt(dynamic value) {
    try {
      if (value == null) return null;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data':
          data?.map((item) => item.toJson()).toList() ?? singleData?.toJson(),
      'totalCount': totalCount,
      'session_id': sessionId,
    };
  }
}

class EnumSpecial {
  int index;
  String? value;
  TextEditingController textController;
  bool? canEdit;

  EnumSpecial(this.index, this.textController, {this.value, this.canEdit});
}
