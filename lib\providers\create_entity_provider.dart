import 'dart:convert';

import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_attributes_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_relationship_model.dart';
import 'package:nsl/services/entity_parse_validation_service.dart';
import 'package:nsl/utils/logger.dart';

class CreateEntityProvider extends ChangeNotifier {
  final EntityParseValidationService entityParseValidationService =
      EntityParseValidationService();
  final entityNameController =
      //  TextEditingController();
      TextEditingController(text: "test");
  final entityIconController =
      //  TextEditingController();
      TextEditingController(text: "test");
  final entityDescriptionController =
      // TextEditingController();
      TextEditingController(text: "test");
  bool isEditMode = true;

  ObjectCreationModel? currentEntity = ObjectCreationModel(id: "");

  bool _isValidating = false;
  String? _validationError;
  static List entityTypes = [
    "master",
    "reference",
    "transaction",
    "configuration",
    "intelligence",
    "aggregate",
    "contextual"
  ];
  List attributesDataTypes = [
    'string',
    'integer',
    'decimal',
    'boolean',
    'date',
    'datetime',
    'text',
    'Enum',
    'Object',
    'Array',
    'MultiValue'
  ];
  List attributesUnique = ['No', 'Yes'];
  List attributesRequired = ['No', 'Yes'];
  List relationshipTypes = [
    'one-to-one',
    'one-to-many',
    'many-to-one',
    'many-to-many'
  ];
  List onDeletes = [
    'Set Null',
    'Restrict',
    'Cascade',
  ];
  List onUpdates = [
    'Set Null',
    'Restrict',
    'Cascade',
  ];
  List foreignKeyTypes = ['Nullable', 'Non-Nullable'];
  String selectedEntityType = entityTypes[0];

  String? get validationError => _validationError;
  bool get isValidating => _isValidating;

  /// Validates the solution and moves to next step
  Future<String?> validateSolution({required bool validate}) async {
    if (entityNameController.text.trim().isEmpty) {
      return "Cannot create entity without name";
    }
    if (entityIconController.text.trim().isEmpty) {
      return "Cannot create entity without icon";
    }
    if (entityDescriptionController.text.trim().isEmpty) {
      return "Cannot create entity without description";
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info('Validating entity: ${entityNameController.text}');
      if (validate) {
        currentEntity = ObjectCreationModel(
          id: currentEntity?.id,
          name: entityNameController.text,
          displayName: entityNameController.text,
          icon: entityIconController.text,
          type: selectedEntityType,
          description: entityDescriptionController.text,
          attributes: currentEntity?.attributes ?? [],
          relationships: currentEntity?.relationships ?? [],
          enumValues: currentEntity?.enumValues ?? [],
        );
        isEditMode = false;
      }

      Logger.info('CreateEntityProvider: Entity validated successfully');
    } catch (e) {
      Logger.error('CreateEntityProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
    return null;
  }

  void updateEntityValue(value) {
    selectedEntityType = value;
    if (currentEntity != null) {
      currentEntity?.type = value;
    }
    notifyListeners();
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }

  void updateEntityAttributeExpansion(bool? value) {
    currentEntity?.attributesExpanded = !(value ?? false);
    notifyListeners();
  }

  void updateEntityRelationShipExpansion(bool? value) {
    currentEntity?.relationshipsExpanded = !(value ?? false);
    notifyListeners();
  }

  void updateEntityEnumExpansion(bool? value) {
    currentEntity?.enumValuesExpanded = !(value ?? false);
    notifyListeners();
  }

  void updateEditMode(value) {
    isEditMode = value;
    notifyListeners();
  }

  update() {
    notifyListeners();
  }

  resetObject() {
    currentEntity = ObjectCreationModel(id: "");
    isEditMode = true;
    notifyListeners();
  }

  Future<List<String>> pickAndReadFileLines() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['txt', 'csv', 'xlsx'],
      withData: true, // needed for web
    );

    if (result == null) return [];

    final file = result.files.single;
    final ext = file.extension?.toLowerCase();
    final bytes = file.bytes;

    if (bytes == null) return [];

    if (ext == 'txt' || ext == 'csv') {
      return utf8.decode(bytes).split(RegExp(r'\r?\n'));
    }

    if (ext == 'xlsx') {
      final excel = Excel.decodeBytes(bytes);
      final List<String> lines = [];
      for (final sheet in excel.tables.keys) {
        for (final row in excel.tables[sheet]!.rows) {
          final line =
              row.map((cell) => cell?.value.toString() ?? '').join(',');
          lines.add(line);
        }
      }
      return lines;
    }

    return [];
  }

  handleMyLibraryObject(ObjectCreationModel myLibObj) async {
    if (myLibObj.id != null) {
      myLibObj.attributes = [];
      myLibObj.relationships = [];
      myLibObj.enumValues = [];
      final entityAttributes = await entityParseValidationService
          .getEntityAttributes(entityId: myLibObj.id);
      final entityRelationships = await entityParseValidationService
          .getEntityRelationships(entityId: myLibObj.id);
      if (entityAttributes != null) {
        List<MongoDraftAttribute> temp = entityAttributes.postgresAttributes ??
            entityAttributes.mongoDrafts ??
            [];
        List<ObjectAttribute> attr = [];
        for (var element in temp) {
          attr.add(ObjectAttribute(
            name: element.name,
            displayName: element.displayName,
            dataType: element.datatype,
            defaultType: element.defaultType,
            description: element.description,
            defaultValue: element.defaultType,
            helperText: element.helperText,
            isForeignKey: element.isForeignKey ?? false,
            isPrimaryKey: element.isPrimaryKey ?? false,
            required: element.isRequired,
            unique: element.isUnique,
          ));
        }
        myLibObj.attributes?.addAll(attr);
      }
      if (entityRelationships != null) {
        List<MongoDraftRelationship> temp =
            entityRelationships.postgresRelationships ??
                entityRelationships.mongoDrafts ??
                [];
        List<EntityRelationship> rel = [];
        for (var element in temp) {
          rel.add(EntityRelationship(
            primaryEntity: element.sourceEntityId,
            primaryKey: element.sourceAttributeId,
            foreignKey: element.targetAttributeId,
            description: element.description,
            foreignKeyType: element.foreignKeyType,
            onDelete: element.onDelete,
            onUpdate: element.onUpdate,
            relationshipType: element.relationshipType,
            relatedEntity: element.targetEntityId,
          ));
        }
        myLibObj.relationships?.addAll(rel);
      }

      currentEntity = myLibObj;
      isEditMode = false;
      notifyListeners();
    }
  }
}
