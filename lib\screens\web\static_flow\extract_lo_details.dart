import 'package:flutter/material.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/providers/creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:nsl/screens/web/static_flow/widgets/lo_input_stack_accordion.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractLoDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractLoDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractLoDetailsMiddleStatic> createState() =>
      _ExtractLoDetailsMiddleStaticState();
}

class _ExtractLoDetailsMiddleStaticState
    extends State<ExtractLoDetailsMiddleStatic> {
  @override
  void initState() {
    super.initState();
    // Initialize roles provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rolesProvider = Provider.of<RolesProvider>(context, listen: false);
      rolesProvider.fetchRoles();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<RolesProvider, GoDetailsProvider, WebHomeProviderStatic>(
      builder: (context, rolesProvider, goDetailsProvider, webProvider, child) {
        // Get the selected LO data
        final selectedIndex = goDetailsProvider.selectedLocalObjectiveIndex;
        LocalObjectivesList? selectedLO = selectedIndex != null &&
                selectedIndex <
                    goDetailsProvider
                        .currentGoModel!.localObjectivesList!.length
            ? goDetailsProvider
                .currentGoModel!.localObjectivesList![selectedIndex]
            : null;
        final solutionName =
            goDetailsProvider.solutionController.text.isNotEmpty
                ? goDetailsProvider.solutionController.text
                : 'Customer Onboarding';

        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: SingleChildScrollView(
            child: _buildContentWithLineNumbers(
                context,
                solutionName,
                selectedLO?.name ?? 'Unknown LO',
                rolesProvider,
                goDetailsProvider),
          ),
        );
      },
    );
  }

  // Build content with line numbers (following extract_go_details pattern)
  Widget _buildContentWithLineNumbers(
      BuildContext context,
      String solutionName,
      String selectedLO,
      RolesProvider rolesProvider,
      GoDetailsProvider goDetailsProvider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    // Line 1: Solution field
    allWidgets.add(_buildLineWithNumber(
        lineNumber++, _buildSolutionField(context, solutionName)));
    allWidgets.add(const SizedBox(height: 11));

    // Line 2: Back arrow with LO name
    allWidgets.add(_buildLineWithNumber(lineNumber++,
        _buildBackArrowWithLOName(context, selectedLO, goDetailsProvider)));
    allWidgets.add(const SizedBox(height: 4));

    // Line 3: Combined Pathway/Agent Type and Dropdowns row
    allWidgets.add(_buildLineWithNumber(
        lineNumber++,
        _buildPathwayAndDropdownsRow(
            context, rolesProvider, goDetailsProvider)));
    allWidgets.add(const SizedBox(height: 8));

    // Line 4: Local Objective Input Stack Accordion
    allWidgets.add(_buildLineWithNumber(
        lineNumber++, _buildLoInputStackAccordion(context, goDetailsProvider)));

    return Stack(
      children: [
        // Continuous vertical line
        Positioned(
          left: 28, // Position after line number (20px width + 8px margin)
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
      ],
    );
  }

  // Helper method for line numbers (following extract_go_details pattern)
  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        SizedBox(
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: ResponsiveFontSizes.labelSmall(context),
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 16), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  // Build back arrow with LO name
  Widget _buildBackArrowWithLOName(BuildContext context, String selectedLO,
      GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            goDetailsProvider.hideLocalObjectiveDetails();
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.arrow_back,
                size: 16,
                color: Colors.black,
              ),
              const SizedBox(width: 8),
              Text(
                selectedLO,
                style: TextStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build solution field
  Widget _buildSolutionField(BuildContext context, String solutionName) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Solution: $solutionName',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.normal,
            color: Color(0xff242424),
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
      ],
    );
  }

  // Build combined pathway and dropdowns row (horizontal layout)
  Widget _buildPathwayAndDropdownsRow(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side: Pathway Type and Agent Type
          Expanded(
            flex: 2,
            child: _buildPathwayAndAgentTypeSection(context, goDetailsProvider),
          ),

          const SizedBox(width: 8),

          // Right side: Function Type, Roles, and Execution Rights dropdowns
          Expanded(
            flex: 3,
            child: _buildDropdownsSection(
                context, rolesProvider, goDetailsProvider),
          ),
        ],
      ),
    );
  }

  // Build pathway and agent type section (left side)
  Widget _buildPathwayAndAgentTypeSection(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    // Get pathway type from the selected LO if available
    final selectedIndex = goDetailsProvider.selectedLocalObjectiveIndex;
    final pathwayType = selectedIndex != null
        ? goDetailsProvider.getPathwaySelectedType(selectedIndex) ??
            'Sequential'
        : 'Sequential';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Pathway Type
        Text(
          'Pathway Type: $pathwayType',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.normal,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),

        const SizedBox(height: 8),

        // Agent Type
        Text(
          'Agent Type: HUMAN',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.normal,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
      ],
    );
  }

  // Build dropdowns section (right side)
  Widget _buildDropdownsSection(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    final selectedIndex = goDetailsProvider.selectedLocalObjectiveIndex ?? 0;

    final roleRowsCount = goDetailsProvider.getLoRoleRowsCount(selectedIndex);

    return Column(
      children: [
        // Main dropdowns row
        Row(
          children: [
            // Function Type dropdown
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Function Type',
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                  const SizedBox(height: 4),
                  CustomDropdownWidget(
                    label: 'Select Function Type',
                    list: const [
                      'create_record',
                      'update_record',
                      'delete_record',
                      'validate_data'
                    ],
                    value: goDetailsProvider.getLoFunctionType(selectedIndex) ??
                        'create_record',
                    onChanged: (value) {
                      if (value != null) {
                        goDetailsProvider.setLoFunctionType(
                            selectedIndex, value);
                      }
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(width: 8),

            // Roles and Execution Rights section with Add More Roles link
            Expanded(
              flex: 2, // Takes space of both Roles and Execution Rights columns
              child: _buildRolesAndExecutionRightsSection(
                  context, rolesProvider, goDetailsProvider),
            ),
          ],
        ),

        // Additional role rows - show all when expanded, only first when collapsed
        if (roleRowsCount > 0) ...[
          // Always show the first additional role row
          const SizedBox(height: 8),
          _buildSingleRoleRow(
              context, rolesProvider, goDetailsProvider, selectedIndex, 0),

          // Show remaining rows only when expanded
          if (goDetailsProvider.isLoRolesSectionExpanded(selectedIndex)) ...[
            for (int i = 1; i < roleRowsCount; i++) ...[
              const SizedBox(height: 8),
              _buildSingleRoleRow(
                  context, rolesProvider, goDetailsProvider, selectedIndex, i),
            ],
          ],
        ],
      ],
    );
  }

  // Build roles and execution rights section with Add More Roles link
  Widget _buildRolesAndExecutionRightsSection(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    final selectedIndex = goDetailsProvider.selectedLocalObjectiveIndex ?? 0;
    final isExpanded =
        goDetailsProvider.isLoRolesSectionExpanded(selectedIndex);

    return Column(
      children: [
        // Add More Roles link with collapse/expand arrow at the top-right
        Row(
          children: [
            Expanded(child: Container()), // Empty space on the left
            Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      goDetailsProvider.addLoRoleRow(selectedIndex);
                    },
                    child: Text(
                      'Add More Roles',
                      style: TextStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w400,
                        color: Colors.blue,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  InkWell(
                    onTap: () {
                      goDetailsProvider
                          .toggleLoRolesSectionExpanded(selectedIndex);
                    },
                    child: Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // Roles and Execution Rights dropdowns row
        Row(
          children: [
            // Roles dropdown
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomDropdownWidget(
                    label: 'Select Role:',
                    list: rolesProvider.isLoading
                        ? ['Loading roles...']
                        : rolesProvider.roles.isEmpty
                            ? ['No roles available']
                            : rolesProvider.roles
                                .map((role) => role.name ?? 'Unknown')
                                .toList(),
                    value: goDetailsProvider
                        .getLoSelectedRole(selectedIndex)
                        ?.name,
                    onChanged: (value) {
                      if (!rolesProvider.isLoading &&
                          value != null &&
                          value != 'Loading roles...' &&
                          value != 'No roles available') {
                        final selectedRole = rolesProvider.roles.firstWhere(
                          (role) => role.name == value,
                          orElse: () => rolesProvider.roles.first,
                        );
                        goDetailsProvider.setLoSelectedRole(
                            selectedIndex, selectedRole);
                      }
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(width: 8),

            // Execution Rights dropdown
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomDropdownWidget(
                    label: 'Select Rights',
                    list: const ['Read', 'Write', 'Execute', 'Admin'],
                    value:
                        goDetailsProvider.getLoExecutionRights(selectedIndex),
                    onChanged: (value) {
                      if (value != null) {
                        goDetailsProvider.setLoExecutionRights(
                            selectedIndex, value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build a single additional role row
  Widget _buildSingleRoleRow(BuildContext context, RolesProvider rolesProvider,
      GoDetailsProvider goDetailsProvider, int loIndex, int rowIndex) {
    final multipleRoles = goDetailsProvider.getLoMultipleRoles(loIndex);
    final multipleExecutionRights =
        goDetailsProvider.getLoMultipleExecutionRights(loIndex);

    return Row(
      children: [
        // Function Type column equivalent (empty space to match main row structure)
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Remove button positioned on the left within the Function Type column space
              InkWell(
                onTap: () {
                  goDetailsProvider.removeLoRoleRow(loIndex, rowIndex);
                },
                child: Container(
                  padding: const EdgeInsets.all(4),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.red,
                  ),
                ),
              ),
              // Rest of the space remains empty to match Function Type column width
              // Expanded(child: Container()),
            ],
          ),
        ),

        const SizedBox(width: 8),

        // Roles and Execution Rights section - EXACT same flex as main row
        Expanded(
          flex: 2, // Same flex: 2 as main row
          child: Row(
            children: [
              // Roles dropdown
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomDropdownWidget(
                      label: 'Select Role:',
                      list: rolesProvider.isLoading
                          ? ['Loading roles...']
                          : rolesProvider.roles.isEmpty
                              ? ['No roles available']
                              : rolesProvider.roles
                                  .map((role) => role.name ?? 'Unknown')
                                  .toList(),
                      value: rowIndex < multipleRoles.length
                          ? multipleRoles[rowIndex]?.name
                          : null,
                      onChanged: (value) {
                        if (!rolesProvider.isLoading &&
                            value != null &&
                            value != 'Loading roles...' &&
                            value != 'No roles available') {
                          final selectedRole = rolesProvider.roles.firstWhere(
                            (role) => role.name == value,
                            orElse: () => rolesProvider.roles.first,
                          );
                          goDetailsProvider.setLoMultipleRole(
                              loIndex, rowIndex, selectedRole);
                        }
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 8),

              // Execution Rights dropdown
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomDropdownWidget(
                      label: 'Select Rights',
                      list: const ['Read', 'Write', 'Execute', 'Admin'],
                      value: rowIndex < multipleExecutionRights.length
                          ? multipleExecutionRights[rowIndex]
                          : null,
                      onChanged: (value) {
                        if (value != null) {
                          goDetailsProvider.setLoMultipleExecutionRights(
                              loIndex, rowIndex, value);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build Local Objective Input Stack Accordion
  Widget _buildLoInputStackAccordion(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    final selectedIndex = goDetailsProvider.selectedLocalObjectiveIndex ?? 0;

    return LoInputStackAccordion(
      title: 'Inputs Stack',
      loIndex: selectedIndex,
      attributeCount: goDetailsProvider
              .getLoSelectedObjectAttributes(selectedIndex)
              ?.length ??
          0,
      // LO-specific data (replaces global provider dependency)
      selectedObject: goDetailsProvider.getLoSelectedObject(selectedIndex),
      selectedObjectAttributes:
          goDetailsProvider.getLoSelectedObjectAttributes(selectedIndex),
      selectedObjects:
          goDetailsProvider.getLoSelectedObjectsList(selectedIndex),
      // LO-specific callbacks
      onSetSelectedObject: (loIndex, object, attributes) {
        goDetailsProvider.setLoSelectedObject(loIndex, object, attributes);
      },
      onAddSelectedObject: (loIndex, object, attributes) {
        goDetailsProvider.addLoSelectedObject(loIndex, object, attributes);
      },
      onRemoveSelectedObject: (loIndex, objectId) {
        goDetailsProvider.removeLoSelectedObject(loIndex, objectId);
      },
      onClearSelectedObjects: (loIndex) {
        goDetailsProvider.clearLoSelectedObjects(loIndex);
      },
      onMyLibraryPressed: () {
        // Handle My Library button press
        Provider.of<CreationProvider>(context, listen: false)
            .isGoMyLibraryClicked = true;
        Provider.of<CreationProvider>(context, listen: false)
            .currentMiddleScreen = 1;
      },
      onAttributeSelected: (value) {
        // Handle attribute selection
      },
    );
  }
}
